<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态对话框层级测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 模拟项目中的模态对话框样式 */
        .modal-form {
            z-index: 10000;
        }
        
        .modal-error {
            z-index: 10001;
        }
        
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-blue-700 mb-8">模态对话框层级测试</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">测试说明</h2>
            <p class="text-gray-600 mb-4">
                此测试页面用于验证药品管理模块中模态对话框的层级问题是否已修复。
                点击下面的按钮来模拟实际使用场景。
            </p>
            
            <div class="space-y-4">
                <button 
                    id="openFormBtn"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                    打开添加药品表单
                </button>
                
                <div class="text-sm text-gray-500">
                    <p>测试步骤：</p>
                    <ol class="list-decimal list-inside space-y-1 mt-2">
                        <li>点击"打开添加药品表单"按钮</li>
                        <li>在表单中点击"触发验证错误"按钮</li>
                        <li>观察错误弹窗是否显示在表单之上</li>
                        <li>先关闭错误弹窗，再关闭表单</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">预期结果</h2>
            <ul class="space-y-2 text-gray-600">
                <li class="flex items-center">
                    <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                    错误弹窗完全可见，不被表单遮挡
                </li>
                <li class="flex items-center">
                    <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                    错误弹窗可以正常关闭
                </li>
                <li class="flex items-center">
                    <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                    关闭错误弹窗后，表单仍然可见
                </li>
                <li class="flex items-center">
                    <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                    表单可以正常关闭
                </li>
            </ul>
        </div>
    </div>

    <!-- 药品表单模态框 (z-index: 10000) -->
    <div id="formModal" class="fixed inset-0 modal-overlay modal-form hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-blue-700">添加药品</h3>
                        <button id="closeFormBtn" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <form class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-black mb-1">药品名称 *</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-600" placeholder="请输入药品名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-black mb-1">批准文号 *</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-600" placeholder="国药准字H20123456">
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                            <button type="button" id="closeFormBtn2" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100">
                                取消
                            </button>
                            <button type="button" id="triggerErrorBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                触发验证错误
                            </button>
                            <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 错误提示模态框 (z-index: 10001) -->
    <div id="errorModal" class="fixed inset-0 modal-overlay modal-error hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-blue-700">操作失败</h3>
                            </div>
                        </div>
                        <button id="closeErrorBtn" class="text-gray-400 hover:text-gray-600 transition-colors duration-150">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700 leading-relaxed">
                                    批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button id="closeErrorBtn2" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200">
                            确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const openFormBtn = document.getElementById('openFormBtn');
        const formModal = document.getElementById('formModal');
        const errorModal = document.getElementById('errorModal');
        const closeFormBtn = document.getElementById('closeFormBtn');
        const closeFormBtn2 = document.getElementById('closeFormBtn2');
        const triggerErrorBtn = document.getElementById('triggerErrorBtn');
        const closeErrorBtn = document.getElementById('closeErrorBtn');
        const closeErrorBtn2 = document.getElementById('closeErrorBtn2');

        // 打开表单模态框
        openFormBtn.addEventListener('click', () => {
            formModal.classList.remove('hidden');
        });

        // 关闭表单模态框
        function closeForm() {
            formModal.classList.add('hidden');
        }
        closeFormBtn.addEventListener('click', closeForm);
        closeFormBtn2.addEventListener('click', closeForm);

        // 触发错误模态框
        triggerErrorBtn.addEventListener('click', () => {
            errorModal.classList.remove('hidden');
        });

        // 关闭错误模态框
        function closeError() {
            errorModal.classList.add('hidden');
        }
        closeErrorBtn.addEventListener('click', closeError);
        closeErrorBtn2.addEventListener('click', closeError);

        // 点击背景关闭模态框
        formModal.addEventListener('click', (e) => {
            if (e.target === formModal) {
                closeForm();
            }
        });

        errorModal.addEventListener('click', (e) => {
            if (e.target === errorModal) {
                closeError();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (!errorModal.classList.contains('hidden')) {
                    closeError();
                } else if (!formModal.classList.contains('hidden')) {
                    closeForm();
                }
            }
        });
    </script>
</body>
</html>
