import { NextRequest, NextResponse } from 'next/server';
import { run, query } from '@/lib/db';

interface RouteParams {
  params: {
    batchId: string;
  };
}

export async function POST(_req: NextRequest, { params }: RouteParams) {
  try {
    const { batchId } = params;

    if (!batchId) {
      return NextResponse.json({
        success: false,
        message: '批次ID不能为空'
      }, { status: 400 });
    }
    
    try {
      // 开始事务
      await run('BEGIN TRANSACTION');
      
      // 1. 查找批次
      const batch = await query(`
        SELECT * FROM 药品批次表 WHERE 编号 = ?
      `, [batchId]);

      if (!batch || batch.length === 0) {
        return NextResponse.json({
          success: false,
          message: '未找到指定批次'
        }, { status: 404 });
      }

      const currentBatch = batch[0];

      // 2. 更新批次状态为过期，并将当前数量设为0
      await run(`
        UPDATE 药品批次表
        SET 状态 = 'expired', 当前数量 = 0, 更新时间 = CURRENT_TIMESTAMP
        WHERE 编号 = ?
      `, [batchId]);

      // 3. 如果批次有剩余数量，需要减少药品库存汇总
      const remainingQuantity = currentBatch.当前数量 || currentBatch.剩余数量 || 0;
      if (remainingQuantity > 0 && currentBatch.状态 === 'active') {
        // 更新药品库存汇总表
        await run(`
          UPDATE 药品库存
          SET 当前库存 = 当前库存 - ?, 更新时间 = CURRENT_TIMESTAMP
          WHERE 药品编号 = ?
        `, [remainingQuantity, currentBatch.药品编号]);

        // 4. 创建库存变动记录
        await run(`
          INSERT INTO 库存变动记录
          (药品编号, 批次编号, 操作类型, 变动数量, 变动前数量, 变动后数量,
           关联单据类型, 操作人, 操作时间, 备注)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
        `, [
          currentBatch.药品编号,
          batchId,
          '过期处理',
          -remainingQuantity, // 负数表示减少
          remainingQuantity,
          0,
          '过期处理单',
          'system',
          `批次 ${currentBatch.批次号} 已标记为过期/作废，剩余数量 ${remainingQuantity} 已清零`
        ]);
      }
      
      // 提交事务
      await run('COMMIT');
      
      return NextResponse.json({ 
        success: true, 
        data: {
          message: '批次已成功标记为过期/作废'
        }
      });
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('标记批次为过期时出错:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理批次过期操作时出错' 
    }, { status: 500 });
  }
} 