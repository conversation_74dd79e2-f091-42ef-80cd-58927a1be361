/**
 * 批准文号验证测试
 * 测试药品批准文号的验证规则是否正确实现
 */

// 模拟验证函数（从实际的验证模块导入）
function validateApprovalNumber(approvalNumber) {
  const pattern = /^国药准字[A-Z]\d{8}$/;
  return {
    isValid: pattern.test(approvalNumber),
    message: pattern.test(approvalNumber) 
      ? null 
      : '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）'
  };
}

// 测试用例
const testCases = [
  // 有效的批准文号
  {
    input: '国药准字H20123456',
    expected: { isValid: true, message: null },
    description: '标准格式的批准文号'
  },
  {
    input: '国药准字Z20210001',
    expected: { isValid: true, message: null },
    description: '中药批准文号'
  },
  {
    input: '国药准字S20190999',
    expected: { isValid: true, message: null },
    description: '生物制品批准文号'
  },
  {
    input: '国药准字J20180888',
    expected: { isValid: true, message: null },
    description: '进口药品批准文号'
  },
  
  // 无效的批准文号
  {
    input: 'H20123456',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '缺少"国药准字"前缀'
  },
  {
    input: '国药准字H2012345',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '数字位数不足（7位）'
  },
  {
    input: '国药准字H201234567',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '数字位数过多（9位）'
  },
  {
    input: '国药准字20123456',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '缺少字母'
  },
  {
    input: '准字H20123456',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '前缀不完整'
  },
  {
    input: '国药准字h20123456',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '字母为小写'
  },
  {
    input: '国药准字H2012345A',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '数字中包含字母'
  },
  {
    input: '',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '空字符串'
  },
  {
    input: '国药准字 H20123456',
    expected: { 
      isValid: false, 
      message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' 
    },
    description: '包含空格'
  }
];

// 运行测试
function runTests() {
  console.log('开始批准文号验证测试...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    const result = validateApprovalNumber(testCase.input);
    const passed = result.isValid === testCase.expected.isValid && 
                   result.message === testCase.expected.message;
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`输入: "${testCase.input}"`);
    console.log(`预期: ${JSON.stringify(testCase.expected)}`);
    console.log(`实际: ${JSON.stringify(result)}`);
    console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`测试总结: ${passedTests}/${totalTests} 个测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试都通过了！批准文号验证规则工作正常。');
  } else {
    console.log('⚠️ 有测试失败，请检查验证规则的实现。');
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateApprovalNumber,
    testCases,
    runTests
  };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.approvalNumberTests = {
    validateApprovalNumber,
    testCases,
    runTests
  };
}

// 自动运行测试（如果直接执行此文件）
if (typeof require !== 'undefined' && require.main === module) {
  runTests();
}
