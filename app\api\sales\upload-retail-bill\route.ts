import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';
import { ensureSettingsTable } from '@/lib/db-init';

// 导入码上放心SDK
const ApiClient = require('../../../../mashangfangxin/index.js').ApiClient;

interface Setting {
  setting_name: string;
  setting_value: string;
}

/**
 * 从系统设置中获取码上放心开放平台配置
 */
async function getConfig() {
  try {
    // 确保"系统设置"表存在
    await ensureSettingsTable();

    // 从数据库获取设置
    const settings = await query('SELECT 设置名称 as setting_name, 设置值 as setting_value FROM 系统设置 WHERE 设置名称 IN (?, ?, ?, ?)', [
      '码上放心开放平台AppKey',
      '码上放心开放平台AppSecret',
      '码上放心开放平台Url',
      '码上放心开放平台RefEntId'
    ]) as Setting[];

    // 将设置转换为配置对象
    const config = {
      appkey: '',
      appsecret: '',
      url: 'http://gw.api.taobao.com/router/rest',
      ref_ent_id: '' // 默认企业ID为空
    };

    // 设置名称映射
    const settingMapping: Record<string, string> = {
      '码上放心开放平台AppKey': 'appkey',
      '码上放心开放平台AppSecret': 'appsecret',
      '码上放心开放平台Url': 'url',
      '码上放心开放平台RefEntId': 'ref_ent_id'
    };

    settings.forEach(setting => {
      const configKey = settingMapping[setting.setting_name];
      if (configKey) {
        if (configKey === 'url' || configKey === 'ref_ent_id') {
          // 对于URL和企业ID，如果数据库值为空则保持默认值
          config[configKey as keyof typeof config] = setting.setting_value || config[configKey as keyof typeof config];
        } else {
          // 对于appkey和appsecret，直接使用数据库值
          config[configKey as keyof typeof config] = setting.setting_value;
        }
      }
    });

    // 如果数据库中没有配置，尝试从环境变量获取
    if (!config.appkey) {
      config.appkey = process.env.MASHANGFANGXIN_APPKEY || '';
    }
    if (!config.appsecret) {
      config.appsecret = process.env.MASHANGFANGXIN_APPSECRET || '';
    }
    if (!config.url) {
      config.url = process.env.MASHANGFANGXIN_URL || 'http://gw.api.taobao.com/router/rest';
    }
    if (!config.ref_ent_id) {
      config.ref_ent_id = process.env.MASHANGFANGXIN_REF_ENT_ID || '';
    }

    return config;
  } catch (error) {
    console.error('获取码上放心配置失败:', error);
    // 返回默认配置
    return {
      appkey: process.env.MASHANGFANGXIN_APPKEY || '',
      appsecret: process.env.MASHANGFANGXIN_APPSECRET || '',
      url: process.env.MASHANGFANGXIN_URL || 'http://gw.api.taobao.com/router/rest',
      ref_ent_id: process.env.MASHANGFANGXIN_REF_ENT_ID || ''
    };
  }
}

/**
 * 创建API客户端实例
 */
function createClient(config: any) {
  return new ApiClient({
    'appkey': config.appkey,
    'appsecret': config.appsecret,
    'url': config.url
  });
}

/**
 * 生成零售单据编号
 */
function generateRetailBillNumber(): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
  const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
  const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `RT-${dateStr}${timeStr}-${randomStr}`;
}

/**
 * 上传零售单据到码上放心平台
 */
async function uploadRetailBillToMashangfangxin(config: any, billData: any, orderInfo: any) {
  const client = createClient(config);

  return new Promise((resolve, reject) => {
    // 使用零售单据上传API
    const apiMethod = 'alibaba.alihealth.drugtrace.top.lsyd.uploadretail';

    // 构建API调用参数
    const apiParams = {
      bill_code: billData.bill_code,
      bill_time: billData.bill_time,
      bill_type: 321, // 321为零售出库
      physic_type: billData.physic_type, // 2特药，3普药
      ref_user_id: config.ref_ent_id, // 上传单据企业的单位编码（药店）
      from_user_id: config.ref_ent_id, // 发货企业entId（药店）
      oper_ic_code: billData.oper_ic_code || '**********', // 操作员IC卡号（默认值）
      oper_ic_name: billData.oper_ic_name || '操作员', // 操作员姓名
      trace_codes: billData.trace_codes.join(','), // 追溯码，多个码用逗号拼接
      customer_id_type: billData.customer_id_type || '5', // 客户证件类型（5为其他）
      customer_id: billData.customer_id || '', // 客户证件号
      user_tel: billData.user_tel || '', // 客户电话
      network_bill_flag: '0', // 网络单据标识（0为非网络单据）
      medic_doctor: billData.medic_doctor || '', // 医师
      medic_dispenser: billData.medic_dispenser || '', // 药师
      user_name: billData.user_name || '散客', // 客户姓名
      user_agent: billData.user_agent || '', // 代理人
      remarks: billData.remarks || '' // 备注信息
    };

    console.log('上传零售单据到码上放心平台，参数:', apiParams);

    client.execute(apiMethod, apiParams, function(error: any, response: any) {
      if (error) {
        console.error('上传零售单据失败:', error);
        reject(error);
      } else {
        console.log('上传零售单据成功，响应数据:', JSON.stringify(response, null, 2));

        // 检查响应中的msg_info和response_success
        if (response && response.response_success === false) {
          // 如果response_success为false，说明上传失败
          const errorMessage = response.msg_info || response.model || '上传失败';
          console.error('上传到码上放心平台失败:', errorMessage);
          reject(new Error(errorMessage));
        } else {
          resolve(response);
        }
      }
    });
  });
}

/**
 * POST 请求处理函数 - 上传零售单据
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { order_id, trace_codes } = body;

    if (!order_id) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：order_id'
      }, { status: 400 });
    }

    if (!trace_codes || !Array.isArray(trace_codes) || trace_codes.length === 0) {
      return NextResponse.json({
        success: false,
        message: '缺少药品追溯码信息'
      }, { status: 400 });
    }

    // 获取码上放心平台配置
    const config = await getConfig();
    
    if (!config.appkey || !config.appsecret || !config.ref_ent_id) {
      return NextResponse.json({
        success: false,
        message: '码上放心平台配置不完整，请在系统设置中配置相关参数'
      }, { status: 400 });
    }

    // 获取销售订单详情
    const orderRecord = await query(
      `SELECT
        o.*,
        c.姓名 as customer_name,
        c.电话 as customer_phone
      FROM 销售订单 o
      LEFT JOIN 客户信息 c ON o.客户编号 = c.编号
      WHERE o.编号 = ?`,
      [order_id]
    );

    if (!orderRecord || orderRecord.length === 0) {
      return NextResponse.json({
        success: false,
        message: '销售订单不存在'
      }, { status: 404 });
    }

    const order = orderRecord[0];
    
    // 生成零售单据编号
    const billCode = generateRetailBillNumber();
    
    // 构建单据数据
    const billData = {
      bill_code: billCode,
      bill_time: new Date().toISOString().slice(0, 19).replace('T', ' '), // 格式化为 yyyy-MM-dd HH:mm:ss
      physic_type: 3, // 默认为普药，实际应根据药品信息判断
      trace_codes: trace_codes, // 追溯码数组
      oper_ic_code: '**********', // 操作员IC卡号（默认值）
      oper_ic_name: '操作员', // 操作员姓名（默认值）
      customer_id_type: '5', // 客户证件类型（5为其他）
      customer_id: '', // 客户证件号
      user_tel: order.customer_phone || '', // 客户电话
      medic_doctor: '', // 医师
      medic_dispenser: '', // 药师
      user_name: order.customer_name || '散客', // 客户姓名
      user_agent: '', // 代理人
      remarks: order.备注 || '' // 备注信息
    };

    try {
      // 上传到码上放心平台
      const uploadResult = await uploadRetailBillToMashangfangxin(config, billData, order);
      
      // 记录上传状态到数据库
      await run(
        `UPDATE 销售订单 SET 
          码上放心单据号 = ?,
          码上放心上传状态 = ?,
          码上放心上传时间 = CURRENT_TIMESTAMP,
          码上放心响应 = ?
        WHERE 编号 = ?`,
        [billCode, 'success', JSON.stringify(uploadResult), order_id]
      );

      return NextResponse.json({
        success: true,
        message: '零售单据上传成功',
        data: {
          bill_code: billCode,
          upload_result: uploadResult,
          trace_codes_count: trace_codes.length
        }
      });

    } catch (uploadError) {
      console.error('上传到码上放心平台失败:', uploadError);

      // 确保错误对象有message属性
      const errorMessage = uploadError instanceof Error ? uploadError.message : String(uploadError);

      // 记录失败状态到数据库
      await run(
        `UPDATE 销售订单 SET
          码上放心单据号 = ?,
          码上放心上传状态 = ?,
          码上放心上传时间 = CURRENT_TIMESTAMP,
          码上放心响应 = ?
        WHERE 编号 = ?`,
        [billCode, 'failed', JSON.stringify({ error: errorMessage }), order_id]
      );

      return NextResponse.json({
        success: false,
        message: '上传到码上放心平台失败: ' + errorMessage,
        data: {
          bill_code: billCode,
          error: errorMessage
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error('处理零售单据上传请求失败:', error);
    return NextResponse.json({
      success: false,
      message: '处理请求失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
