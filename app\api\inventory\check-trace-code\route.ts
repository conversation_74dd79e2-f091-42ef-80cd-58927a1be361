import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const traceCode = searchParams.get('code');

    if (!traceCode) {
      return NextResponse.json({
        success: false,
        message: '请提供追溯码'
      }, { status: 400 });
    }

    // 确保药品追溯码记录表存在
    try {
      const tableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='药品追溯码记录'"
      );

      if (!tableExists || tableExists.length === 0) {
        // 创建药品追溯码记录表
        await run(`
          CREATE TABLE 药品追溯码记录 (
            编号 INTEGER PRIMARY KEY AUTOINCREMENT,
            库存记录编号 INTEGER,
            销售订单编号 INTEGER,
            药品编号 INTEGER NOT NULL,
            追溯码 TEXT NOT NULL,
            操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '销售')),
            批次号 TEXT,
            有效期 DATE,
            生产厂家 TEXT,
            药品名称 TEXT,
            规格 TEXT,
            操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')) DEFAULT 'pending',
            码上放心响应 TEXT,
            备注 TEXT,
            FOREIGN KEY (库存记录编号) REFERENCES 库存记录(编号),
            FOREIGN KEY (销售订单编号) REFERENCES 销售订单(编号),
            FOREIGN KEY (药品编号) REFERENCES 药品信息(编号)
          )
        `);

        // 创建索引
        await run('CREATE INDEX IF NOT EXISTS idx_药品追溯码记录_追溯码 ON 药品追溯码记录(追溯码)');
        await run('CREATE INDEX IF NOT EXISTS idx_药品追溯码记录_药品编号 ON 药品追溯码记录(药品编号)');
      }
    } catch (tableError) {
      console.error('创建药品追溯码记录表失败:', tableError);
    }

    // 检查追溯码是否已经在药品追溯码记录表中使用过
    const existingRecords = await query(
      `SELECT
        编号,
        药品编号,
        操作类型,
        操作时间,
        追溯码
      FROM 药品追溯码记录
      WHERE 追溯码 = ?`,
      [traceCode]
    );

    const isUsed = existingRecords.length > 0;

    return NextResponse.json({
      success: true,
      data: {
        isUsed,
        records: existingRecords,
        message: isUsed ? '该追溯码已被使用' : '追溯码可以使用'
      }
    });

  } catch (error) {
    console.error('检查追溯码失败:', error);
    return NextResponse.json({
      success: false,
      message: '检查追溯码时发生错误'
    }, { status: 500 });
  }
}
