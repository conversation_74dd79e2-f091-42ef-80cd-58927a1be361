import { NextRequest, NextResponse } from 'next/server';
import { query, get } from '@/lib/db';

// 获取下一个订单编号
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const orderType = searchParams.get('type') || 'SO'; // 默认为销售订单

    // 获取系统设置中的订单编号位数
    const settingsResult = await get(
      'SELECT 设置值 as setting_value FROM 系统设置 WHERE 设置名称 = ?',
      ['订单编号位数']
    );
    const orderNumberDigits = parseInt(settingsResult?.setting_value || '4');

    // 生成今日日期字符串
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // 查询今日该类型订单的最大序号
    const todayPrefix = `${orderType}-${dateStr}`;
    const maxOrderResult = await get(`
      SELECT 订单编号 as order_number
      FROM 销售订单
      WHERE 订单编号 LIKE ?
      ORDER BY 订单编号 DESC
      LIMIT 1
    `, [`${todayPrefix}%`]);

    let nextSequence = 1;
    
    if (maxOrderResult && maxOrderResult.order_number) {
      // 从最大订单号中提取序号
      const orderNumber = maxOrderResult.order_number;
      const sequencePart = orderNumber.substring(todayPrefix.length);
      const currentSequence = parseInt(sequencePart);
      
      if (!isNaN(currentSequence)) {
        nextSequence = currentSequence + 1;
      }
    }

    // 生成新的订单编号
    const sequenceNumber = String(nextSequence).padStart(orderNumberDigits, '0');
    const newOrderNumber = `${todayPrefix}${sequenceNumber}`;

    return NextResponse.json({
      success: true,
      data: {
        orderNumber: newOrderNumber,
        sequence: nextSequence,
        dateStr: dateStr,
        orderType: orderType
      }
    });
  } catch (error) {
    console.error('生成订单编号失败:', error);
    return NextResponse.json(
      { success: false, message: '生成订单编号失败' },
      { status: 500 }
    );
  }
}
