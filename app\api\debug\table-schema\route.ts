import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET() {
  try {
    // 获取销售订单表的结构信息
    const tableInfo = await query("PRAGMA table_info(销售订单)");
    
    // 获取表的创建语句
    const createStatement = await query(
      "SELECT sql FROM sqlite_master WHERE type='table' AND name='销售订单'"
    );
    
    return NextResponse.json({
      success: true,
      tableInfo,
      createStatement: createStatement[0]?.sql || 'No create statement found'
    });
  } catch (error) {
    console.error('获取表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
