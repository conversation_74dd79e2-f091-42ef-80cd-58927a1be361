import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

// 获取订单列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // 获取订单总数 - 使用英文表名
    const countResult = await get('SELECT COUNT(*) as total FROM 销售订单');
    const total = countResult?.total || 0;

    // 获取订单列表 - 使用正确的字段名，按订单编号降序排列（最新订单在前）
    const 销售订单 = await query(`
      SELECT
        o.编号 as id,
        o.订单编号 as orderNumber,
        o.总金额 as amount,
        o.状态 as status,
        o.创建时间 as date,
        c.姓名 as customer,
        c.电话 as phone
      FROM 销售订单 o
      LEFT JOIN 客户信息 c ON o.客户编号 = c.编号
      ORDER BY o.订单编号 DESC, o.创建时间 DESC
      LIMIT ? OFFSET ?
    `, [limit, offset]);

    // 获取今日销售统计 - 使用中文字段名
    const today = new Date().toISOString().split('T')[0];
    const todayStats = await get(`
      SELECT
        COUNT(*) as orderCount,
        SUM(总金额) as totalSales
      FROM 销售订单
      WHERE date(创建时间) = ?
    `, [today]);

    // 获取平均订单金额 - 使用中文字段名
    const avgOrderAmount = await get(`
      SELECT AVG(总金额) as average
      FROM 销售订单
    `);

    // 获取热销商品 - 使用正确的中文字段名
    let topProducts = [];

    // 只有当有订单时才查询热销商品
    if (total > 0) {
      topProducts = await query(`
        SELECT
          p.名称 as name,
          SUM(od.数量) as count,
          SUM(od.小计) as amount
        FROM 订单明细 od
        JOIN 药品信息 p ON od.药品编号 = p.编号
        JOIN 销售订单 o ON od.订单编号 = o.编号
        GROUP BY p.编号
        ORDER BY count DESC
        LIMIT 5
      `);
    }

    return NextResponse.json({
      success: true,
      data: {
        orders: 销售订单,
        total,
        todayStats: {
          orderCount: todayStats.orderCount || 0,
          totalSales: todayStats.totalSales || 0
        },
        avgOrderAmount: avgOrderAmount.average || 0,
        topProducts
      }
    });
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return NextResponse.json(
      { success: false, message: '获取订单列表失败' },
      { status: 500 }
    );
  }
}

// 创建新订单
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const {
      orderNumber,
      customer,
      items,
      totalAmount,
      discountAmount = 0,
      payableAmount,
      receivedAmount,
      changeAmount = 0,
      paymentMethod,
      note
    } = data;

    console.log('创建订单数据:', {
      orderNumber,
      paymentMethod,
      paymentMethodType: typeof paymentMethod,
      paymentMethodValue: JSON.stringify(paymentMethod),
      paymentMethodLength: paymentMethod?.length,
      totalAmount,
      payableAmount
    });

    // 验证支付方式
    const validPaymentMethods = ['cash', 'card', 'alipay', 'wechat', 'medical_card'];
    console.log('有效支付方式:', validPaymentMethods);
    console.log('支付方式是否有效:', validPaymentMethods.includes(paymentMethod));

    if (!validPaymentMethods.includes(paymentMethod)) {
      console.error('无效的支付方式:', paymentMethod);
      return NextResponse.json(
        { success: false, error: `无效的支付方式: ${paymentMethod}` },
        { status: 400 }
      );
    }

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 1. 检查或创建客户 - 使用英文表名
      let customerId = null;
      if (customer && customer.name) {
        // 检查客户是否已存在
        const existingCustomer = await get(
          'SELECT 编号 FROM 客户信息 WHERE 电话 = ?',
          [customer.phone]
        );

        if (existingCustomer) {
          customerId = existingCustomer.编号;
        } else {
          // 创建新客户
          const result = await run(
            'INSERT INTO 客户信息 (姓名, 电话) VALUES (?, ?)',
            [customer.name, customer.phone]
          );
          customerId = result.lastID;
        }
      }

      // 2. 创建订单 - 使用正确的中文字段名
      const orderResult = await run(
        `INSERT INTO 销售订单 (
          订单编号, 客户编号, 总金额, 折扣金额,
          应付金额, 实付金额, 找零金额, 支付方式, 状态, 备注
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderNumber,
          customerId,
          totalAmount,
          discountAmount,
          payableAmount,
          receivedAmount,
          changeAmount,
          paymentMethod || 'cash',
          data.status || 'completed',
          note || ''
        ]
      );

      const orderId = orderResult.lastID;

      // 3. 创建订单明细并更新库存
      for (const item of items) {
        // 简化处理，不使用复杂的批次管理
        console.log(`处理药品: ${item.medicine.name}, 数量: ${item.quantity}`);

        // 创建订单明细 - 使用正确的中文字段名
        await run(
          `INSERT INTO 订单明细 (
            订单编号, 药品编号, 数量, 单价, 小计, 追溯码
          ) VALUES (?, ?, ?, ?, ?, ?)`,
          [
            orderId,
            item.medicine.id,
            item.quantity,
            item.medicine.price,
            item.subtotal,
            item.traceCode || null
          ]
        );

        // 更新药品总库存
        await run(
          `UPDATE 药品信息 SET 库存数量 = 库存数量 - ? WHERE 编号 = ?`,
          [item.quantity, item.medicine.id]
        );

        // 记录库存变动 - 简化处理
        try {
          // 首先检查库存记录表的结构
          const tableInfo = await query('PRAGMA table_info(库存记录)');
          const columns = tableInfo.map(col => col.name);

          // 构建SQL语句和参数
          let sql, params;

          if (columns.includes('quantity_change')) {
            // 如果表有quantity_change列
            sql = `
              INSERT INTO 库存记录 (
                product_id, quantity_change, change_type, reference_id, reference_type, operator_id
              ) VALUES (?, ?, ?, ?, ?, ?)
            `;
            params = [
              item.medicine.id,
              -item.quantity, // 负数表示减少
              'sale',
              orderId,
              'order',
              1 // 默认操作员ID
            ];
          } else if (columns.includes('quantity')) {
            // 如果表使用quantity而不是quantity_change
            sql = `
              INSERT INTO 库存记录 (
                product_id, quantity, type, reference_number, note
              ) VALUES (?, ?, ?, ?, ?)
            `;
            params = [
              item.medicine.id,
              -item.quantity, // 负数表示减少
              'out',
              `order-${orderNumber}`,
              `销售订单出库: ${orderNumber}`
            ];
          } else {
            // 如果表结构完全不匹配，记录日志但不中断流程
            console.log('库存记录表结构不匹配，跳过库存记录');
            console.log('可用列:', columns.join(', '));
          }

          // 如果有SQL语句，执行插入
          if (sql && params) {
            await run(sql, params);
            console.log(`成功记录库存变动: 产品ID ${item.medicine.id}, 数量 ${-item.quantity}`);
          }
        } catch (inventoryError) {
          // 即使记录库存变动失败，也不要中断订单创建
          console.error('记录库存变动失败:', inventoryError);
          console.log('继续处理订单，但库存记录可能不完整');
        }
      }

      // 处理有追溯码的商品出库
      const traceCodeItems = items.filter(item => item.traceCode);
      if (traceCodeItems.length > 0) {
        try {
          console.log(`处理 ${traceCodeItems.length} 个有追溯码的商品出库`);

          // 调用出库处理（这里直接在同一事务中处理，而不是调用API）
          for (const item of traceCodeItems) {
            // 记录追溯码使用信息
            if (item.traceCode) {
              await run(
                `INSERT INTO 药品追溯码记录 (
                  销售订单编号, 药品编号, 追溯码, 操作类型,
                  批次号, 有效期, 生产厂家, 药品名称, 规格, 备注
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                  orderId,
                  item.medicine.id,
                  item.traceCode,
                  '销售',
                  item.batchInfo?.batchNo || null,
                  item.batchInfo?.expireDate || null,
                  item.batchInfo?.manufacturer || null,
                  item.batchInfo?.drugName || null,
                  item.batchInfo?.specification || null,
                  `销售订单 ${orderNumber} 出库`
                ]
              );
            }
          }

          console.log('追溯码记录创建成功');
        } catch (traceError) {
          console.error('处理追溯码记录失败:', traceError);
          // 不中断订单创建，但记录错误
        }
      }

      // 提交事务
      await run('COMMIT');

      return NextResponse.json({
        success: true,
        data: {
          orderId,
          orderNumber
        }
      });
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('创建订单失败:', error);
    return NextResponse.json(
      { success: false, message: `创建订单失败: ${error.message}` },
      { status: 500 }
    );
  }
}
