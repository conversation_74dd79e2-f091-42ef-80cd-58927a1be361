import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 获取即将过期的批次
 * GET /api/inventory/expiring-batches?days=30
 */
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const days = parseInt(searchParams.get('days') || '30'); // 默认30天内过期
    
    // 计算过期日期阈值
    const today = new Date();
    const expiryThreshold = new Date();
    expiryThreshold.setDate(today.getDate() + days);
    
    const todayStr = today.toISOString().split('T')[0];
    const thresholdStr = expiryThreshold.toISOString().split('T')[0];
    
    // 查询即将过期和已过期的批次
    const expiringBatches = await query(`
      SELECT
        b.编号 as id,
        b.药品编号 as product_id,
        b.批次号 as batch_number,
        b.生产日期 as production_date,
        b.有效期 as expiry_date,
        COALESCE(b.入库数量, b.数量, 0) as quantity,
        COALESCE(b.当前数量, b.剩余数量, 0) as remaining_quantity,
        b.供应商编号 as supplier_id,
        s.名称 as supplier_name,
        COALESCE(b.入库成本价, b.成本价) as purchase_price,
        b.状态 as status,
        b.创建时间 as created_at,
        b.更新时间 as updated_at,
        p.名称 as product_name,
        p.规格 as specification,
        p.剂型 as dosage_form,
        p.生产厂家 as manufacturer,
        -- 计算距离过期的天数
        CAST((julianday(b.有效期) - julianday('now')) AS INTEGER) as days_to_expiry
      FROM 药品批次表 b
      LEFT JOIN 供应商 s ON b.供应商编号 = s.编号
      LEFT JOIN 药品信息 p ON b.药品编号 = p.编号
      WHERE (
        -- 包含活跃状态的批次（即将过期或临期）
        (b.状态 = 'active' AND b.有效期 IS NOT NULL AND b.有效期 <= ?)
        OR
        -- 包含已标记为过期的批次
        (b.状态 = 'expired' AND b.有效期 IS NOT NULL)
      )
      ORDER BY b.有效期 ASC, b.剩余数量 DESC
    `, [thresholdStr]);
    
    // 分类批次：已过期、即将过期（7天内）、临期（30天内）
    const categorizedBatches = {
      expired: [] as any[],
      critical: [] as any[], // 7天内过期
      warning: [] as any[],   // 30天内过期
      total_count: expiringBatches.length,
      total_value: 0
    };
    
    let totalValue = 0;
    
    for (const batch of expiringBatches) {
      const daysToExpiry = batch.days_to_expiry;
      const batchValue = (batch.remaining_quantity || 0) * (batch.purchase_price || 0);
      totalValue += batchValue;

      // 添加计算字段
      batch.batch_value = batchValue;

      // 判断过期状态：优先考虑批次状态，然后考虑天数
      if (batch.status === 'expired' || daysToExpiry < 0) {
        batch.expiry_status = 'expired';
        categorizedBatches.expired.push(batch);
      } else if (daysToExpiry <= 7) {
        batch.expiry_status = 'critical';
        categorizedBatches.critical.push(batch);
      } else {
        batch.expiry_status = 'warning';
        categorizedBatches.warning.push(batch);
      }
    }
    
    categorizedBatches.total_value = totalValue;
    
    return NextResponse.json({
      success: true,
      data: categorizedBatches
    });
    
  } catch (error) {
    console.error('获取即将过期批次失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取即将过期批次失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
